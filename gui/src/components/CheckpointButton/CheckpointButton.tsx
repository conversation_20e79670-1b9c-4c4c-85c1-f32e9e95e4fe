import { ArrowUturnLeftIcon } from "@heroicons/react/24/outline";
import { useCallback } from "react";
import { useAppDispatch, useAppSelector } from "../../redux/hooks";
import { restoreCheckpointThunk } from "../../redux/thunks/checkpoint";
import { Button } from "../";
import { ToolTip } from "../gui/Tooltip";

interface CheckpointButtonProps {
  className?: string;
}

export function CheckpointButton({ className }: CheckpointButtonProps) {
  const dispatch = useAppDispatch();
  const historyLabel = useAppSelector(
    (state) => state.session.checkpoint.historyLabel,
  );
  const mode = useAppSelector((state) => state.session.mode);

  const handleRestore = useCallback(async () => {
    if (!historyLabel) return;

    const confirmed = window.confirm(
      `确定要回滚到checkpoint吗？这将撤销本次会话中AI对文件的所有修改。\n\n历史标识: ${historyLabel}`,
    );

    if (confirmed) {
      try {
        await dispatch(restoreCheckpointThunk({ historyLabel }));
        // 可以添加成功提示
        console.log("Checkpoint restored successfully");
      } catch (error) {
        console.error("Failed to restore checkpoint:", error);
        // 可以添加错误提示
      }
    }
  }, [historyLabel, dispatch]);

  // 只在智能体或流程化智能体模式下且有checkpoint时显示
  const shouldShow =
    (mode === "agent" || mode === "structured-agent") && historyLabel;

  if (!shouldShow) {
    return null;
  }

  return (
    <div className={className}>
      <Button
        data-tooltip-id="checkpoint-button-tooltip"
        onClick={handleRestore}
        className="flex items-center gap-2 rounded-md border border-gray-300 bg-white px-3 py-1.5 text-sm text-gray-700 shadow-sm hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
      >
        <ArrowUturnLeftIcon className="h-4 w-4" />
        <span className="hidden sm:inline">回滚到Checkpoint</span>
        <span className="sm:hidden">回滚</span>
      </Button>
      <ToolTip id="checkpoint-button-tooltip" place="top">
        回滚到会话开始时的文件状态
        <br />
        历史标识: {historyLabel}
      </ToolTip>
    </div>
  );
}
