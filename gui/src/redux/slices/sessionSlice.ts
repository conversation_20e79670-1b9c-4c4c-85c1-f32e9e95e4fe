import {
  ActionReducer<PERSON>ap<PERSON><PERSON>er,
  AsyncThunk,
  PayloadAction,
  createSelector,
  createSlice,
} from "@reduxjs/toolkit";
import { JSONContent } from "@tiptap/react";
import {
  ApplyState,
  ChatHistoryItem,
  ChatMessage,
  ContextItem,
  ContextItemWithId,
  FileSymbolMap,
  MessageModes,
  PromptLog,
  RuleWithSource,
  Session,
  SessionMetadata,
  StructuredAgentWorkflowState,
  StructuredAgentStepType,
} from "core";
import { NEW_SESSION_TITLE } from "core/util/constants";
import { renderChatMessage } from "core/util/messageContent";
import { findUriInDirs, getUriPathBasename } from "core/util/uri";
import { v4 as uuidv4 } from "uuid";
import { addToolCallDeltaToState } from "../../util/toolCallState";
import { RootState } from "../store";
import { streamResponseThunk } from "../thunks/streamResponse";
import { findCurrentToolCall, findToolCall } from "../util";

// We need this to handle reorderings (e.g. a mid-array deletion) of the messages array.
// The proper fix is adding a UUID to all chat messages, but this is the temp workaround.
export type ChatHistoryItemWithMessageId = ChatHistoryItem & {
  message: ChatMessage & { id: string };
};

type SessionState = {
  lastSessionId?: string;
  allSessionMetadata: SessionMetadata[];
  history: ChatHistoryItemWithMessageId[];
  isStreaming: boolean;
  title: string;
  id: string;
  streamAborter: AbortController;
  mainEditorContentTrigger?: JSONContent | undefined;
  symbols: FileSymbolMap;
  mode: MessageModes;
  isInEdit: boolean;
  codeBlockApplyStates: {
    states: ApplyState[];
    curIndex: number;
  };
  newestToolbarPreviewForInput: Record<string, string>;
  structuredAgentWorkflow: StructuredAgentWorkflowState;
  checkpoint: {
    historyLabel?: string;
    isFirstMessage: boolean;
  };
};

const initialState: SessionState = {
  allSessionMetadata: [],
  history: [],
  isStreaming: false,
  title: NEW_SESSION_TITLE,
  id: uuidv4(),
  streamAborter: new AbortController(),
  symbols: {},
  mode: "chat",
  isInEdit: false,
  codeBlockApplyStates: {
    states: [],
    curIndex: 0,
  },
  lastSessionId: undefined,
  newestToolbarPreviewForInput: {},
  structuredAgentWorkflow: {
    isActive: false,
    currentStep: "requirement-input",
    stepIndex: 0,
    totalSteps: 5,
    isWaitingForConfirmation: false,
    stepHistoryStartIndex: undefined,
  },
  checkpoint: {
    historyLabel: undefined,
    isFirstMessage: true,
  },
};

export const sessionSlice = createSlice({
  name: "session",
  initialState,
  reducers: {
    addPromptCompletionPair: (
      state,
      { payload }: PayloadAction<PromptLog[]>,
    ) => {
      if (!state.history.length) {
        return;
      }

      const lastMessage = state.history[state.history.length - 1];

      lastMessage.promptLogs = lastMessage.promptLogs
        ? lastMessage.promptLogs.concat(payload)
        : payload;

      // Inactive thinking for reasoning models when '</think>' tag is not received on request completion
      if (lastMessage.reasoning?.active) {
        lastMessage.reasoning.active = false;
        lastMessage.reasoning.endAt = Date.now();
      }
    },
    setActive: (state) => {
      state.isStreaming = true;
    },
    setIsGatheringContext: (state, { payload }: PayloadAction<boolean>) => {
      const curMessage = state.history.at(-1);
      if (curMessage) {
        curMessage.isGatheringContext = payload;
      }
    },
    clearLastEmptyResponse: (state) => {
      if (state.history.length < 2) {
        return;
      }
      const lastMessage = state.history[state.history.length - 1];

      // Only clear in the case of an empty message
      if (!lastMessage.message.content.length) {
        state.mainEditorContentTrigger =
          state.history[state.history.length - 2].editorState;
        state.history = state.history.slice(0, -2);
        // TODO is this logic correct for tool use conversations?
        // Maybe slice at last index of "user" role message?
      }
    },
    // Trigger value picked up by editor with isMainInput to set its content
    setMainEditorContentTrigger: (
      state,
      action: PayloadAction<JSONContent | undefined>,
    ) => {
      state.mainEditorContentTrigger = action.payload;
    },
    updateFileSymbols: (state, action: PayloadAction<FileSymbolMap>) => {
      state.symbols = {
        ...state.symbols,
        ...action.payload,
      };
    },
    setContextItemsAtIndex: (
      state,
      {
        payload: { index, contextItems },
      }: PayloadAction<{
        index: number;
        contextItems: ChatHistoryItem["contextItems"];
      }>,
    ) => {
      if (state.history[index]) {
        state.history[index].contextItems = contextItems;
      }
    },
    submitEditorAndInitAtIndex: (
      state,
      {
        payload,
      }: PayloadAction<{
        index: number;
        editorState: JSONContent;
      }>,
    ) => {
      const { index, editorState } = payload;

      if (state.history.length && index < state.history.length) {
        // Resubmission - update input message, truncate history after resubmit with new empty response message
        if (index % 2 === 1) {
          console.warn(
            "Corrupted history: resubmitting at odd index, shouldn't happen",
          );
        }
        const historyItem = state.history[index];

        historyItem.message.content = ""; // IMPORTANT - this is quickly updated by resolveEditorContent based on editor state prior to streaming
        historyItem.editorState = payload.editorState;
        historyItem.contextItems = [];

        state.history = state.history.slice(0, index + 1).concat({
          message: {
            id: uuidv4(),
            role: "assistant",
            content: "", // IMPORTANT - this is subsequently updated by response streaming
          },
          contextItems: [],
        });
      } else {
        // New input/response messages
        state.history = state.history.concat([
          {
            message: {
              id: uuidv4(),
              role: "user",
              content: "", // IMPORTANT - this is quickly updated by resolveEditorContent based on editor state prior to streaming
            },
            contextItems: [],
            editorState,
          },
          {
            message: {
              id: uuidv4(),
              role: "assistant",
              content: "", // IMPORTANT - this is subsequently updated by response streaming
            },
            contextItems: [],
          },
        ]);
      }

      state.isStreaming = true;
    },
    deleteMessage: (state, action: PayloadAction<number>) => {
      // Deletes the current assistant message and the previous user message
      state.history.splice(action.payload - 1, 2);
    },
    editMessage: (
      state,
      action: PayloadAction<{ index: number; content: string }>,
    ) => {
      const { index, content } = action.payload;
      if (state.history[index]) {
        state.history[index].message.content = content;
      }
    },
    updateHistoryItemAtIndex: (
      state,
      {
        payload,
      }: PayloadAction<{
        index: number;
        updates: Partial<ChatHistoryItemWithMessageId>;
      }>,
    ) => {
      const { index, updates } = payload;
      if (index !== 0 && !state.history[index]) {
        console.error(
          `attempting to update history item at nonexistent index ${index}`,
          updates,
        );
        return;
      }
      state.history[index] = {
        ...state.history[index],
        ...updates,
      };
    },
    addContextItemsAtIndex: (
      state,
      {
        payload,
      }: PayloadAction<{
        index: number;
        contextItems: ContextItemWithId[];
      }>,
    ) => {
      const historyItem = state.history[payload.index];

      if (!historyItem) {
        return;
      }

      historyItem.contextItems = [
        ...historyItem.contextItems,
        ...payload.contextItems,
      ];
    },
    setAppliedRulesAtIndex: (
      state,
      {
        payload,
      }: PayloadAction<{
        index: number;
        appliedRules: RuleWithSource[];
      }>,
    ) => {
      if (state.history[payload.index]) {
        state.history[payload.index].appliedRules = payload.appliedRules;
      }
    },
    setInactive: (state) => {
      const curMessage = state.history.at(-1);

      if (curMessage) {
        curMessage.isGatheringContext = false;
      }

      state.isStreaming = false;
    },
    abortStream: (state) => {
      state.streamAborter.abort();
      state.streamAborter = new AbortController();
    },
    streamUpdate: (state, action: PayloadAction<ChatMessage[]>) => {
      if (state.history.length) {
        for (const message of action.payload) {
          const lastItem = state.history[state.history.length - 1];
          const lastMessage = lastItem.message;

          if (message.role === "thinking" && message.redactedThinking) {
            console.log("add redacted_thinking blocks");

            state.history.push({
              message: {
                role: "thinking",
                content: "internal reasoning is hidden due to safety reasons",
                redactedThinking: message.redactedThinking,
                id: uuidv4(),
              },
              contextItems: [],
            });
            continue;
          }

          if (
            lastMessage.role !== message.role ||
            // This is for when a tool call comes immediately before/after tool call
            (lastMessage.role === "assistant" &&
              message.role === "assistant" &&
              // Last message isn't completely new
              !(!lastMessage.toolCalls?.length && !lastMessage.content) &&
              // And there's a difference in tool call presence
              (lastMessage.toolCalls?.length ?? 0) !==
                (message.toolCalls?.length ?? 0))
          ) {
            // Create a new message
            const historyItem: ChatHistoryItemWithMessageId = {
              message: {
                ...message,
                content: renderChatMessage(message),
                id: uuidv4(),
              },
              contextItems: [],
            };
            if (message.role === "assistant" && message.toolCalls?.[0]) {
              const toolCallDelta = message.toolCalls[0];
              historyItem.toolCallState = addToolCallDeltaToState(
                toolCallDelta,
                undefined,
              );
            }
            state.history.push(historyItem);
          } else {
            // Add to the existing message
            if (message.content) {
              const messageContent = renderChatMessage(message);
              if (messageContent.includes("<think>")) {
                lastItem.reasoning = {
                  startAt: Date.now(),
                  active: true,
                  text: messageContent.replace("<think>", "").trim(),
                };
              } else if (
                lastItem.reasoning?.active &&
                messageContent.includes("</think>")
              ) {
                const [reasoningEnd, answerStart] =
                  messageContent.split("</think>");
                lastItem.reasoning.text += reasoningEnd.trimEnd();
                lastItem.reasoning.active = false;
                lastItem.reasoning.endAt = Date.now();
                lastMessage.content += answerStart.trimStart();
              } else if (lastItem.reasoning?.active) {
                lastItem.reasoning.text += messageContent;
              } else {
                // Note this only works because new message above
                // was already rendered from parts to string
                lastMessage.content += messageContent;
              }
            } else if (message.role === "thinking" && message.signature) {
              if (lastMessage.role === "thinking") {
                console.log("add signature", message.signature);
                lastMessage.signature = message.signature;
              }
            } else if (
              message.role === "assistant" &&
              message.toolCalls?.[0] &&
              lastMessage.role === "assistant"
            ) {
              // Intentionally only supporting one tool call for now.
              const toolCallDelta = message.toolCalls[0];
              const newToolCallState = addToolCallDeltaToState(
                toolCallDelta,
                lastItem.toolCallState,
              );
              lastItem.toolCallState = newToolCallState;
              lastMessage.toolCalls = [newToolCallState.toolCall];
            }
          }
        }
      }
    },
    newSession: (state, { payload }: PayloadAction<Session | undefined>) => {
      state.lastSessionId = state.id;

      state.streamAborter.abort();
      state.streamAborter = new AbortController();

      state.isStreaming = false;
      state.symbols = {};

      // 重置结构化智能体工作流程状态
      state.structuredAgentWorkflow = {
        isActive: false,
        currentStep: "requirement-input",
        stepIndex: 0,
        totalSteps: 5,
        isWaitingForConfirmation: false,
        stepHistoryStartIndex: undefined,
      };

      // 重置checkpoint状态
      state.checkpoint = {
        historyLabel: undefined,
        isFirstMessage: true,
      };

      if (payload) {
        state.history = payload.history as any;
        state.title = payload.title;
        state.id = payload.sessionId;
      } else {
        state.history = [];
        state.title = NEW_SESSION_TITLE;
        state.id = uuidv4();
      }
    },
    updateSessionTitle: (state, { payload }: PayloadAction<string>) => {
      state.title = payload;
    },
    setAllSessionMetadata: (
      state,
      { payload }: PayloadAction<SessionMetadata[]>,
    ) => {
      state.allSessionMetadata = payload;
    },
    //////////////////////////////////////////////////////////////////////////////////
    // These are for optimistic session metadata updates, especially for History page
    addSessionMetadata: (
      state,
      { payload }: PayloadAction<SessionMetadata>,
    ) => {
      state.allSessionMetadata = [...state.allSessionMetadata, payload];
    },
    updateSessionMetadata: (
      state,
      {
        payload,
      }: PayloadAction<
        {
          sessionId: string;
        } & Partial<SessionMetadata>
      >,
    ) => {
      state.allSessionMetadata = state.allSessionMetadata.map((session) =>
        session.sessionId === payload.sessionId
          ? {
              ...session,
              ...payload,
            }
          : session,
      );
      if (payload.title && payload.sessionId === state.id) {
        state.title = payload.title;
      }
    },
    deleteSessionMetadata: (state, { payload }: PayloadAction<string>) => {
      // Note, should not be allowed to delete current session from chat session
      state.allSessionMetadata = state.allSessionMetadata.filter(
        (session) => session.sessionId !== payload,
      );
    },
    //////////////////////////////////////////////////////////////////////////////////
    addHighlightedCode: (
      state,
      {
        payload,
      }: PayloadAction<{ rangeInFileWithContents: any; edit: boolean }>,
    ) => {
      let contextItems =
        state.history[state.history.length - 1].contextItems ?? [];

      contextItems = contextItems.map((item) => {
        return { ...item, editing: false };
      });

      const { relativePathOrBasename } = findUriInDirs(
        payload.rangeInFileWithContents.filepath,
        window.workspacePaths ?? [],
      );
      const fileName = getUriPathBasename(
        payload.rangeInFileWithContents.filepath,
      );

      const lineNums = `(${
        payload.rangeInFileWithContents.range.start.line + 1
      }-${payload.rangeInFileWithContents.range.end.line + 1})`;

      contextItems.push({
        name: `${fileName} ${lineNums}`,
        description: relativePathOrBasename,
        id: {
          providerTitle: "code",
          itemId: uuidv4(),
        },
        content: payload.rangeInFileWithContents.contents,
        editing: true,
        editable: true,
        uri: {
          type: "file",
          value: payload.rangeInFileWithContents.filepath,
        },
      });

      state.history[state.history.length - 1].contextItems = contextItems;
    },
    updateApplyState: (state, { payload }: PayloadAction<ApplyState>) => {
      const applyState = state.codeBlockApplyStates.states.find(
        (state) => state.streamId === payload.streamId,
      );

      if (!applyState) {
        state.codeBlockApplyStates.states.push(payload);
      } else {
        applyState.status = payload.status ?? applyState.status;
        applyState.numDiffs = payload.numDiffs ?? applyState.numDiffs;
        applyState.filepath = payload.filepath ?? applyState.filepath;
        applyState.fileContent = payload.fileContent ?? applyState.fileContent;
      }

      if (payload.status === "done") {
        state.codeBlockApplyStates.curIndex++;
      }
    },
    resetNextCodeBlockToApplyIndex: (state) => {
      state.codeBlockApplyStates.curIndex = 0;
    },

    // TOOL CALL STATE
    setToolGenerated: (
      state,
      action: PayloadAction<{
        toolCallId: string;
      }>,
    ) => {
      const toolCallState = findToolCall(
        state.history,
        action.payload.toolCallId,
      );
      if (toolCallState) {
        toolCallState.status = "generated";
      }
    },
    updateToolCallOutput: (
      state,
      action: PayloadAction<{
        toolCallId: string;
        contextItems: ContextItem[];
      }>,
    ) => {
      const toolCallState = findToolCall(
        state.history,
        action.payload.toolCallId,
      );
      if (toolCallState) {
        toolCallState.output = action.payload.contextItems;
      }
    },
    cancelToolCall: (
      state,
      action: PayloadAction<{
        toolCallId: string;
      }>,
    ) => {
      const toolCallState = findToolCall(
        state.history,
        action.payload.toolCallId,
      );
      if (toolCallState) {
        toolCallState.status = "canceled";
      }
    },
    errorToolCall: (
      state,
      action: PayloadAction<{
        toolCallId: string;
      }>,
    ) => {
      const toolCallState = findToolCall(
        state.history,
        action.payload.toolCallId,
      );
      if (toolCallState) {
        toolCallState.status = "errored";
      }
    },
    acceptToolCall: (
      state,
      action: PayloadAction<{
        toolCallId: string;
      }>,
    ) => {
      const toolCallState = findToolCall(
        state.history,
        action.payload.toolCallId,
      );
      if (toolCallState) {
        toolCallState.status = "done";
      }
    },
    setToolCallCalling: (
      state,
      action: PayloadAction<{
        toolCallId: string;
      }>,
    ) => {
      const toolCallState = findToolCall(
        state.history,
        action.payload.toolCallId,
      );
      if (toolCallState) {
        toolCallState.status = "calling";
      }
    },
    setMode: (state, action: PayloadAction<MessageModes>) => {
      state.mode = action.payload;
    },
    setIsInEdit: (state, action: PayloadAction<boolean>) => {
      state.isInEdit = action.payload;
    },
    setNewestToolbarPreviewForInput: (
      state,
      {
        payload,
      }: PayloadAction<{
        inputId: string;
        contextItemId: string;
      }>,
    ) => {
      state.newestToolbarPreviewForInput[payload.inputId] =
        payload.contextItemId;
    },
    // 结构化Agent工作流程相关的reducers
    startStructuredAgentWorkflow: (state) => {
      state.structuredAgentWorkflow = {
        isActive: true,
        currentStep: "requirement-input",
        stepIndex: 1,
        totalSteps: 5,
        isWaitingForConfirmation: false,
        stepHistoryStartIndex: state.history.length, // 记录当前步骤开始的历史记录索引
      };
    },
    updateStructuredAgentStep: (
      state,
      {
        payload,
      }: PayloadAction<{
        step: StructuredAgentStepType;
        stepIndex: number;
        data?: Partial<StructuredAgentWorkflowState>;
      }>,
    ) => {
      // 如果是新的步骤，更新步骤历史记录开始索引
      if (state.structuredAgentWorkflow.currentStep !== payload.step) {
        state.structuredAgentWorkflow.stepHistoryStartIndex =
          state.history.length;
      }
      state.structuredAgentWorkflow.currentStep = payload.step;
      state.structuredAgentWorkflow.stepIndex = payload.stepIndex;
      if (payload.data) {
        Object.assign(state.structuredAgentWorkflow, payload.data);
      }
    },
    setStructuredAgentWaitingForConfirmation: (
      state,
      { payload }: PayloadAction<boolean>,
    ) => {
      state.structuredAgentWorkflow.isWaitingForConfirmation = payload;
    },
    setStructuredAgentUserFeedback: (
      state,
      { payload }: PayloadAction<string>,
    ) => {
      state.structuredAgentWorkflow.userFeedback = payload;
    },
    resetStructuredAgentWorkflow: (state) => {
      state.structuredAgentWorkflow = {
        isActive: false,
        currentStep: "requirement-input",
        stepIndex: 0,
        totalSteps: 5,
        isWaitingForConfirmation: false,
        stepHistoryStartIndex: undefined,
      };
    },
    stopStructuredAgentWorkflow: (state) => {
      state.structuredAgentWorkflow = {
        isActive: false,
        currentStep: "requirement-input",
        stepIndex: 0,
        totalSteps: 5,
        isWaitingForConfirmation: false,
        stepHistoryStartIndex: undefined,
      };
      // 同时停止流式输出
      state.streamAborter.abort();
      state.streamAborter = new AbortController();
      state.isStreaming = false;
    },
    // Checkpoint相关的reducers
    setCheckpointLabel: (state, { payload }: PayloadAction<string>) => {
      state.checkpoint.historyLabel = payload;
    },
    clearCheckpoint: (state) => {
      state.checkpoint.historyLabel = undefined;
    },
    setIsFirstMessage: (state, { payload }: PayloadAction<boolean>) => {
      state.checkpoint.isFirstMessage = payload;
    },
  },
  selectors: {
    selectIsGatheringContext: (state) => {
      const curHistoryItem = state.history.at(-1);
      return curHistoryItem?.isGatheringContext || false;
    },
  },
  extraReducers: (builder) => {
    addPassthroughCases(builder, [streamResponseThunk]);
  },
});

function addPassthroughCases(
  builder: ActionReducerMapBuilder<SessionState>,
  thunks: AsyncThunk<any, any, any>[],
) {
  thunks.forEach((thunk) => {
    builder
      .addCase(thunk.fulfilled, (state, action) => {})
      .addCase(thunk.rejected, (state, action) => {})
      .addCase(thunk.pending, (state, action) => {});
  });
}

export const selectCurrentToolCall = createSelector(
  (store: RootState) => store.session.history,
  (history) => {
    return findCurrentToolCall(history);
  },
);

export const selectApplyStateByStreamId = createSelector(
  [
    (state: RootState) => state.session.codeBlockApplyStates.states,
    (state: RootState, streamId?: string) => streamId,
  ],
  (states, streamId) => {
    return states.find((state) => state.streamId === streamId);
  },
);

export const selectApplyStateByToolCallId = createSelector(
  [
    (state: RootState) => state.session.codeBlockApplyStates.states,
    (state: RootState, toolCallId?: string) => toolCallId,
  ],
  (states, toolCallId) => {
    return states.find((state) => state.toolCallId === toolCallId);
  },
);

export const {
  updateFileSymbols,
  setContextItemsAtIndex,
  addContextItemsAtIndex,
  setAppliedRulesAtIndex,
  setInactive,
  streamUpdate,
  newSession,
  updateSessionTitle,
  addHighlightedCode,
  addPromptCompletionPair,
  setActive,
  submitEditorAndInitAtIndex,
  updateHistoryItemAtIndex,
  clearLastEmptyResponse,
  setMainEditorContentTrigger,
  deleteMessage,
  editMessage,
  setIsGatheringContext,
  resetNextCodeBlockToApplyIndex,
  updateApplyState,
  abortStream,
  setToolCallCalling,
  cancelToolCall,
  errorToolCall,
  acceptToolCall,
  setToolGenerated,
  updateToolCallOutput,
  setMode,
  setAllSessionMetadata,
  addSessionMetadata,
  updateSessionMetadata,
  deleteSessionMetadata,
  setNewestToolbarPreviewForInput,
  setIsInEdit,
  startStructuredAgentWorkflow,
  updateStructuredAgentStep,
  setStructuredAgentWaitingForConfirmation,
  setStructuredAgentUserFeedback,
  resetStructuredAgentWorkflow,
  stopStructuredAgentWorkflow,
  setCheckpointLabel,
  clearCheckpoint,
  setIsFirstMessage,
} = sessionSlice.actions;

export const { selectIsGatheringContext } = sessionSlice.selectors;

export default sessionSlice.reducer;
