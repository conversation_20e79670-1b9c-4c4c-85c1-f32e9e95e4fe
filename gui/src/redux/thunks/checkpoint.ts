import { createAsyncThunk } from "@reduxjs/toolkit";
import { CheckpointInfo } from "core";
import { setCheckpoint, clearCheckpoint, setIsFirstMessage } from "../slices/sessionSlice";
import { ThunkApiType } from "../store";

// 创建checkpoint
export const createCheckpointThunk = createAsyncThunk<
  CheckpointInfo,
  { description?: string },
  ThunkApiType
>(
  "checkpoint/create",
  async ({ description }, { dispatch, extra, getState }) => {
    const state = getState();
    const sessionId = state.session.id;

    const checkpointInfo = await extra.ideMessenger.request("checkpoint/create", {
      sessionId,
      description,
    });

    dispatch(setCheckpoint(checkpointInfo));
    return checkpointInfo;
  },
);

// 恢复checkpoint
export const restoreCheckpointThunk = createAsyncThunk<
  void,
  { checkpointId: string },
  ThunkApiType
>(
  "checkpoint/restore",
  async ({ checkpointId }, { dispatch, extra }) => {
    await extra.ideMessenger.request("checkpoint/restore", {
      checkpointId,
    });

    // 清除当前checkpoint状态
    dispatch(clearCheckpoint());
  },
);

// 列出checkpoints
export const listCheckpointsThunk = createAsyncThunk<
  CheckpointInfo[],
  undefined,
  ThunkApiType
>(
  "checkpoint/list",
  async (_, { extra, getState }) => {
    const state = getState();
    const sessionId = state.session.id;

    return await extra.ideMessenger.request("checkpoint/list", {
      sessionId,
    });
  },
);

// 删除checkpoint
export const deleteCheckpointThunk = createAsyncThunk<
  void,
  { checkpointId: string },
  ThunkApiType
>(
  "checkpoint/delete",
  async ({ checkpointId }, { dispatch, extra, getState }) => {
    await extra.ideMessenger.request("checkpoint/delete", {
      checkpointId,
    });

    const state = getState();
    // 如果删除的是当前checkpoint，清除状态
    if (state.session.checkpoint.current?.id === checkpointId) {
      dispatch(clearCheckpoint());
    }
  },
);

// 检测并创建第一条消息的checkpoint
export const checkAndCreateFirstMessageCheckpoint = createAsyncThunk<
  void,
  undefined,
  ThunkApiType
>(
  "checkpoint/checkAndCreateFirst",
  async (_, { dispatch, getState }) => {
    const state = getState();
    
    // 检查是否是智能体或流程化智能体模式
    const isAgentMode = state.session.mode === "agent" || state.session.mode === "structured-agent";
    
    // 检查是否是第一条消息且在智能体模式下
    if (isAgentMode && state.session.checkpoint.isFirstMessage && state.session.history.length === 1) {
      // 创建checkpoint
      await dispatch(createCheckpointThunk({
        description: "Session started - checkpoint created before first AI response",
      }));
      
      // 标记不再是第一条消息
      dispatch(setIsFirstMessage(false));
    }
  },
);
