import { createAsyncThunk } from "@reduxjs/toolkit";
import {
  setCheckpointLabel,
  clearCheckpoint,
  setIsFirstMessage,
} from "../slices/sessionSlice";
import { ThunkApiType } from "../store";

// 创建checkpoint
export const createCheckpointThunk = createAsyncThunk<
  void,
  { description?: string },
  ThunkApiType
>(
  "checkpoint/create",
  async ({ description }, { dispatch, extra, getState }) => {
    const state = getState();
    const sessionId = state.session.id;

    console.log("createCheckpointThunk called", { sessionId, description });

    const historyLabel = await extra.ideMessenger.request("checkpoint/create", {
      sessionId,
      description,
    });

    console.log("createCheckpointThunk received historyLabel", historyLabel);
    dispatch(setCheckpointLabel(historyLabel));
    return historyLabel;
  },
);

// 恢复checkpoint
export const restoreCheckpointThunk = createAsyncThunk<
  void,
  { historyLabel: string },
  ThunkApiType
>(
  "checkpoint/restore",
  async ({ historyLabel }, { dispatch, extra, getState }) => {
    await extra.ideMessenger.request("checkpoint/restore", {
      historyLabel,
    });

    // 清除当前checkpoint状态
    dispatch(clearCheckpoint());
  },
);

// 检测并创建第一条消息的checkpoint
export const checkAndCreateFirstMessageCheckpoint = createAsyncThunk<
  void,
  undefined,
  ThunkApiType
>("checkpoint/checkAndCreateFirst", async (_, { dispatch, getState }) => {
  const state = getState();

  console.log("checkAndCreateFirstMessageCheckpoint called", {
    mode: state.session.mode,
    isFirstMessage: state.session.checkpoint.isFirstMessage,
    historyLength: state.session.history.length,
  });

  // 检查是否是智能体或流程化智能体模式
  const isAgentMode =
    state.session.mode === "agent" || state.session.mode === "structured-agent";

  // 检查是否是第一条消息且在智能体模式下
  if (
    isAgentMode &&
    state.session.checkpoint.isFirstMessage &&
    state.session.history.length === 1
  ) {
    console.log("Creating checkpoint for first message in agent mode");
    // 创建checkpoint
    await dispatch(
      createCheckpointThunk({
        description:
          "Session started - checkpoint created before first AI response",
      }),
    );

    // 标记不再是第一条消息
    dispatch(setIsFirstMessage(false));
  } else {
    console.log("Checkpoint creation skipped", {
      isAgentMode,
      isFirstMessage: state.session.checkpoint.isFirstMessage,
      historyLength: state.session.history.length,
    });
  }
});
