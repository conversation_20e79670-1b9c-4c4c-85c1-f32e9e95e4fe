package com.github.continuedev.continueintellijextension.protocol

data class CheckpointInfo(
    val id: String,
    val sessionId: String,
    val timestamp: Long,
    val modifiedFiles: List<String>,
    val description: String?
)

data class CreateLocalHistoryCheckpointParams(
    val checkpointInfo: CheckpointInfo
)

data class RestoreFromLocalHistoryCheckpointParams(
    val checkpointId: String
)

data class ListLocalHistoryCheckpointsParams(
    val sessionId: String
)

data class DeleteLocalHistoryCheckpointParams(
    val checkpointId: String
)
