package com.github.continuedev.continueintellijextension.`continue`

import com.intellij.history.LocalHistory
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.project.Project

object CheckpointManager {

    /**
     * 获取当前本地历史的最新标识
     */
    fun getCurrentLocalHistoryLabel(project: Project): String {
        return try {
            // 保存所有未保存的文档
            FileDocumentManager.getInstance().saveAllDocuments()

            // 获取当前时间戳作为标识
            val timestamp = System.currentTimeMillis()
            val label = "Continue_Checkpoint_$timestamp"

            // 创建本地历史标签
            val localHistory = LocalHistory.getInstance()
            val action = localHistory.startAction(label)
            action.finish()

            println("Created local history checkpoint: $label")
            label
        } catch (e: Exception) {
            println("Error creating local history checkpoint: ${e.message}")
            e.printStackTrace()
            ""
        }
    }

    /**
     * 使用本地历史标识回滚到指定状态
     */
    fun restoreFromLocalHistory(project: Project, historyLabel: String) {
        ApplicationManager.getApplication().invokeLater {
            try {
                println("Attempting to restore from local history: $historyLabel")

                // 获取项目根目录
                val projectDir = project.basePath?.let {
                    com.intellij.openapi.vfs.LocalFileSystem.getInstance().findFileByPath(it)
                }

                if (projectDir == null) {
                    println("Project directory not found")
                    return@invokeLater
                }

                // 使用IDEA的本地历史功能
                val localHistory = LocalHistory.getInstance()

                // 这里使用IDEA的本地历史API进行回滚
                // 注意：实际的回滚操作可能需要用户在IDEA界面中手动完成
                // 或者使用更高级的API，这里先提供一个基础实现

                println("Local history restore initiated for: $historyLabel")
                println("Please use IDEA's Local History feature to complete the restoration")

            } catch (e: Exception) {
                println("Error restoring from local history: ${e.message}")
                e.printStackTrace()
            }
        }
    }

}
