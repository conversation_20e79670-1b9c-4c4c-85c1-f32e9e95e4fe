package com.github.continuedev.continueintellijextension.`continue`

import com.github.continuedev.continueintellijextension.protocol.CheckpointInfo
import com.intellij.history.LocalHistory
import com.intellij.history.LocalHistoryAction
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.command.WriteCommandAction
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.LocalFileSystem
import com.intellij.openapi.vfs.VirtualFile
import java.io.File
import java.util.concurrent.ConcurrentHashMap

object CheckpointManager {
    // 存储checkpoint信息的内存映射
    private val checkpoints = ConcurrentHashMap<String, CheckpointInfo>()
    // 存储session到checkpoint的映射
    private val sessionCheckpoints = ConcurrentHashMap<String, MutableList<String>>()
    // 存储checkpoint到LocalHistoryAction的映射
    private val checkpointActions = ConcurrentHashMap<String, LocalHistoryAction>()

    /**
     * 创建一个checkpoint
     */
    fun createCheckpoint(project: Project, checkpointInfo: CheckpointInfo) {
        ApplicationManager.getApplication().invokeLater {
            try {
                // 保存所有未保存的文档
                FileDocumentManager.getInstance().saveAllDocuments()
                
                // 创建本地历史标签
                val localHistory = LocalHistory.getInstance()
                val action = localHistory.startAction(checkpointInfo.description ?: "Continue Checkpoint")
                
                // 存储checkpoint信息
                checkpoints[checkpointInfo.id] = checkpointInfo
                checkpointActions[checkpointInfo.id] = action
                
                // 添加到session映射
                sessionCheckpoints.computeIfAbsent(checkpointInfo.sessionId) { mutableListOf() }
                    .add(checkpointInfo.id)
                
                // 完成本地历史动作
                action.finish()
                
                println("Checkpoint created: ${checkpointInfo.id}")
            } catch (e: Exception) {
                println("Error creating checkpoint: ${e.message}")
                e.printStackTrace()
            }
        }
    }

    /**
     * 恢复到指定的checkpoint
     */
    fun restoreCheckpoint(project: Project, checkpointId: String) {
        ApplicationManager.getApplication().invokeLater {
            try {
                val checkpointInfo = checkpoints[checkpointId]
                if (checkpointInfo == null) {
                    println("Checkpoint not found: $checkpointId")
                    return@invokeLater
                }

                WriteCommandAction.runWriteCommandAction(project) {
                    // 获取本地历史
                    val localHistory = LocalHistory.getInstance()
                    
                    // 获取项目根目录
                    val projectDir = project.basePath?.let { LocalFileSystem.getInstance().findFileByPath(it) }
                    if (projectDir == null) {
                        println("Project directory not found")
                        return@runWriteCommandAction
                    }

                    // 获取历史记录
                    val gateway = localHistory.gateway
                    val revisions = gateway.getRevisionsFor(projectDir)
                    
                    // 查找对应时间戳的修订版本
                    val targetRevision = revisions.find { revision ->
                        Math.abs(revision.timestamp - checkpointInfo.timestamp) < 5000 // 5秒容差
                    }
                    
                    if (targetRevision != null) {
                        // 恢复到指定版本
                        gateway.revertToRevision(projectDir, targetRevision)
                        println("Restored to checkpoint: $checkpointId")
                    } else {
                        println("Could not find revision for checkpoint: $checkpointId")
                    }
                }
            } catch (e: Exception) {
                println("Error restoring checkpoint: ${e.message}")
                e.printStackTrace()
            }
        }
    }

    /**
     * 列出指定session的所有checkpoints
     */
    fun listCheckpoints(project: Project, sessionId: String): List<CheckpointInfo> {
        val checkpointIds = sessionCheckpoints[sessionId] ?: return emptyList()
        return checkpointIds.mapNotNull { checkpoints[it] }
            .sortedByDescending { it.timestamp }
    }

    /**
     * 删除指定的checkpoint
     */
    fun deleteCheckpoint(project: Project, checkpointId: String) {
        try {
            val checkpointInfo = checkpoints.remove(checkpointId)
            if (checkpointInfo != null) {
                // 从session映射中移除
                sessionCheckpoints[checkpointInfo.sessionId]?.remove(checkpointId)
                
                // 移除LocalHistoryAction引用
                checkpointActions.remove(checkpointId)
                
                println("Checkpoint deleted: $checkpointId")
            }
        } catch (e: Exception) {
            println("Error deleting checkpoint: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 清理指定session的所有checkpoints
     */
    fun clearSessionCheckpoints(sessionId: String) {
        val checkpointIds = sessionCheckpoints.remove(sessionId) ?: return
        checkpointIds.forEach { checkpointId ->
            checkpoints.remove(checkpointId)
            checkpointActions.remove(checkpointId)
        }
    }
}
