package src.main.java;

/**
 * HelloWorld类 - 一个简单的Java示例类
 * 包含基本的数学运算方法
 */
public class HelloWorld {
    /**
     * 主方法 - 程序的入口点
     * @param args 命令行参数数组
     */
    public static void main(String[] args) {
        // 输出欢迎信息
        System.out.println("Hello, World!");
    }

    /**
     * 加法运算 - 计算两个整数的和
     * @param a 第一个加数
     * @param b 第二个加数
     * @return 两数之和
     */
    public static int add(int a, int b) {
        return a + b;
    }
    /**
     * 乘法运算 - 计算两个整数的乘积
     * @param a 第一个因数
     * @param b 第二个因数
     * @return 两数之积
     */
    public static int multiply(int a, int b) {
        return a * b;
    }

    /**
     * 除法运算 - 计算两个整数的商
     * @param a 被除数
     * @param b 除数
     * @return 两数之商（浮点数结果）
     * @throws ArithmeticException 当除数为0时抛出此异常
     */
    public static double divide(int a, int b) {
        // 检查除数是否为0，防止除零错误
        if (b == 0) {
            throw new ArithmeticException("除数不能为0");
        }
        // 将结果转换为double类型以保留小数部分
        return (double) a / b;
    }
}
